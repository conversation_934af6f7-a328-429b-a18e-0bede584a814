import { NextRequest, NextResponse } from 'next/server';
import { challongeAPI } from '@/lib/challonge';

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const tournamentId = searchParams.get('tournamentId');

    if (!tournamentId) {
      return NextResponse.json(
        { error: 'Tournament ID is required' },
        { status: 400 }
      );
    }

    const participants = await challongeAPI.getParticipants(tournamentId);
    return NextResponse.json({ participants });
  } catch (error) {
    console.error('Error fetching participants:', error);
    return NextResponse.json(
      { error: 'Failed to fetch participants' },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { tournamentId, name } = body;

    if (!tournamentId || !name) {
      return NextResponse.json(
        { error: 'Tournament ID and name are required' },
        { status: 400 }
      );
    }

    const participant = await challongeAPI.addParticipant(tournamentId, { name });

    return NextResponse.json({ participant });
  } catch (error) {
    console.error('Error adding participant:', error);
    return NextResponse.json(
      { error: 'Failed to add participant' },
      { status: 500 }
    );
  }
}

export async function PUT(request: NextRequest) {
  try {
    const body = await request.json();
    const { tournamentId, participantId, seed } = body;

    if (!tournamentId || !participantId) {
      return NextResponse.json(
        { error: 'Tournament ID and participant ID are required' },
        { status: 400 }
      );
    }

    const updateData: { seed?: number } = {};
    if (seed !== undefined) {
      updateData.seed = seed;
    }

    const participant = await challongeAPI.updateParticipant(tournamentId, participantId, updateData);

    return NextResponse.json({ participant });
  } catch (error) {
    console.error('Error updating participant:', error);
    return NextResponse.json(
      { error: 'Failed to update participant' },
      { status: 500 }
    );
  }
}
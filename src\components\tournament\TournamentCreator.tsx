'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';

interface TournamentCreatorProps {
  onTournamentCreated: (tournamentId: string) => void;
}

export default function TournamentCreator({ onTournamentCreated }: TournamentCreatorProps) {
  const [formData, setFormData] = useState({
    name: '',
    tournament_type: 'single elimination'
  });
  const [participants, setParticipants] = useState<string[]>(['']);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const addParticipant = () => {
    setParticipants(prev => [...prev, '']);
  };

  const removeParticipant = (index: number) => {
    setParticipants(prev => prev.filter((_, i) => i !== index));
  };

  const updateParticipant = (index: number, value: string) => {
    setParticipants(prev => prev.map((p, i) => i === index ? value : p));
  };

  const createTournament = async () => {
    try {
      setLoading(true);
      setError(null);

      if (!formData.name) {
        throw new Error('Tournament name is required');
      }

      const response = await fetch('/api/challonge/tournaments', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(formData)
      });

      if (!response.ok) {
        throw new Error('Failed to create tournament');
      }

      const data = await response.json();
      const tournamentId = data.tournament.id;

      const validParticipants = participants.filter(p => p.trim().length > 0);
      
      if (validParticipants.length > 0) {
        // Add participants one by one in the exact order they were entered
        for (let i = 0; i < validParticipants.length; i++) {
          const name = validParticipants[i];

          const response = await fetch('/api/challonge/participants', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json'
            },
            body: JSON.stringify({
              tournamentId,
              name: name.trim(),
              seed: i + 1  // Explicitly set seed based on order
            })
          });

          if (!response.ok) {
            const errorData = await response.json();
            throw new Error(`Failed to add participant: ${name} - ${errorData.error || 'Unknown error'}`);
          }

          // Small delay between additions to ensure order
          if (i < validParticipants.length - 1) {
            await new Promise(resolve => setTimeout(resolve, 100));
          }
        }
      }

      onTournamentCreated(tournamentId);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred');
    } finally {
      setLoading(false);
    }
  };

  return (
    <Card className="w-full max-w-2xl mx-auto bg-slate-800 border-slate-700">
      <CardHeader>
        <CardTitle className="text-white">Create KDF Tournament</CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div>
          <label className="block text-sm font-medium mb-1 text-white">Tournament Name</label>
          <input
            type="text"
            value={formData.name}
            onChange={(e) => handleInputChange('name', e.target.value)}
            className="w-full p-2 border border-slate-600 rounded-md bg-slate-700 text-white placeholder-gray-400"
            placeholder="Enter tournament name"
          />
        </div>

        <div>
          <label className="block text-sm font-medium mb-1 text-white">Tournament Type</label>
          <Select value={formData.tournament_type} onValueChange={(value) => handleInputChange('tournament_type', value)}>
            <SelectTrigger className="border-slate-600 bg-slate-700 text-white">
              <SelectValue />
            </SelectTrigger>
            <SelectContent className="bg-slate-700 border-slate-600">
              <SelectItem value="single elimination" className="text-white hover:bg-slate-600">Single Elimination</SelectItem>
              <SelectItem value="double elimination" className="text-white hover:bg-slate-600">Double Elimination</SelectItem>
              <SelectItem value="round robin" className="text-white hover:bg-slate-600">Round Robin</SelectItem>
              <SelectItem value="swiss" className="text-white hover:bg-slate-600">Swiss</SelectItem>
            </SelectContent>
          </Select>
        </div>


        <div>
          <label className="block text-sm font-medium mb-2 text-white">Participants</label>
          <div className="space-y-2">
            {participants.map((participant, index) => (
              <div key={index} className="flex gap-2">
                <input
                  type="text"
                  value={participant}
                  onChange={(e) => updateParticipant(index, e.target.value)}
                  className="flex-1 p-2 border border-slate-600 rounded-md bg-slate-700 text-white placeholder-gray-400"
                  placeholder={`Participant ${index + 1}`}
                />
                {participants.length > 1 && (
                  <Button
                    type="button"
                    variant="outline"
                    size="sm"
                    onClick={() => removeParticipant(index)}
                    className="border-slate-500 text-white hover:bg-slate-600"
                  >
                    Remove
                  </Button>
                )}
              </div>
            ))}
            <Button
              type="button"
              variant="outline"
              size="sm"
              onClick={addParticipant}
              className="border-slate-500 text-white hover:bg-slate-600"
            >
              Add Participant
            </Button>
          </div>
        </div>

        {error && (
          <div className="text-red-400 text-sm">{error}</div>
        )}

        <Button
          onClick={createTournament}
          disabled={loading}
          className="w-full bg-blue-600 hover:bg-blue-700"
        >
          {loading ? 'Creating Tournament...' : 'Create Tournament'}
        </Button>
      </CardContent>
    </Card>
  );
}
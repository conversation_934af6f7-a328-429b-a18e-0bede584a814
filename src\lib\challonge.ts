import axios from 'axios';

const CHALLONGE_API_BASE = 'https://api.challonge.com/v1';
const CHALLONGE_USERNAME = process.env.CHALLONGE_USERNAME;
const CHALLONGE_API_KEY = process.env.CHALLONGE_API_KEY;

export interface Tournament {
  id: string;
  name: string;
  url: string;
  state: string;
  tournament_type: string;
  started_at: string | null;
  completed_at: string | null;
  participants_count: number;
  game_name: string;
  description: string;
}

export interface Participant {
  id: string;
  tournament_id: string;
  name: string;
  seed: number;
  email: string | null;
  username: string | null;
  final_rank: number | null;
  misc: string | null;
}

export interface Match {
  id: string;
  tournament_id: string;
  state: string;
  player1_id: string | null;
  player2_id: string | null;
  winner_id: string | null;
  loser_id: string | null;
  started_at: string | null;
  completed_at: string | null;
  round: number;
  player1_votes: number;
  player2_votes: number;
  scores_csv: string;
}

class ChallongeAPI {
  private async makeRequest(endpoint: string, method: 'GET' | 'POST' | 'PUT' | 'DELETE' = 'GET', data?: any) {
    if (!CHALLONGE_USERNAME || !CHALLONGE_API_KEY) {
      throw new Error('Challonge username and API key are required. Please set CHALLONGE_USERNAME and CHALLONGE_API_KEY in your .env file.');
    }

    try {
      const response = await axios({
        method,
        url: `${CHALLONGE_API_BASE}${endpoint}`,
        auth: {
          username: CHALLONGE_USERNAME,
          password: CHALLONGE_API_KEY
        },
        headers: {
          'Content-Type': 'application/json'
        },
        data
      });
      
      return response.data;
    } catch (error: any) {
      console.error(`Error making ${method} request to ${endpoint}:`, error);
      
      // Log specific validation errors if available
      if (error.response?.data?.errors) {
        console.error('Validation errors:', error.response.data.errors);
      }
      
      throw error;
    }
  }

  async createTournament(tournamentData: {
    name: string;
    tournament_type: string;
  }): Promise<Tournament> {
    // Create a unique URL with timestamp to avoid duplicates
    const baseUrl = tournamentData.name.toLowerCase().replace(/[^a-z0-9]/g, '_').replace(/_+/g, '_');
    const timestamp = Date.now().toString().slice(-6); // Last 6 digits of timestamp
    const url = `${baseUrl}_${timestamp}`;
    
    const requestData = {
      tournament: {
        name: tournamentData.name,
        url: url,
        tournament_type: tournamentData.tournament_type
      }
    };
    
    const response = await this.makeRequest('/tournaments.json', 'POST', requestData);
    return response.tournament;
  }

  async getTournament(tournamentId: string): Promise<Tournament> {
    const response = await this.makeRequest(`/tournaments/${tournamentId}.json`);
    return response.tournament;
  }

  async getTournaments(): Promise<Tournament[]> {
    const response = await this.makeRequest('/tournaments.json');
    return response.map((item: any) => item.tournament);
  }

  async startTournament(tournamentId: string): Promise<Tournament> {
    const response = await this.makeRequest(`/tournaments/${tournamentId}/start.json`, 'POST');
    return response.tournament;
  }

  async finalizeTournament(tournamentId: string): Promise<Tournament> {
    const response = await this.makeRequest(`/tournaments/${tournamentId}/finalize.json`, 'POST');
    return response.tournament;
  }

  async resetTournament(tournamentId: string): Promise<Tournament> {
    const response = await this.makeRequest(`/tournaments/${tournamentId}/reset.json`, 'POST');
    return response.tournament;
  }

  async randomizeParticipants(tournamentId: string): Promise<Tournament> {
    const response = await this.makeRequest(`/tournaments/${tournamentId}/participants/randomize.json`, 'POST');
    return response.tournament;
  }

  async getParticipants(tournamentId: string): Promise<Participant[]> {
    const response = await this.makeRequest(`/tournaments/${tournamentId}/participants.json`);
    return response.map((item: any) => item.participant);
  }

  async addParticipant(tournamentId: string, participantData: {
    name: string;
    seed?: number;
  }): Promise<Participant> {
    const response = await this.makeRequest(`/tournaments/${tournamentId}/participants.json`, 'POST', {
      participant: participantData
    });
    return response.participant;
  }

  async updateParticipant(tournamentId: string, participantId: string, participantData: {
    name?: string;
    email?: string;
    username?: string;
    seed?: number;
    misc?: string;
  }): Promise<Participant> {
    const response = await this.makeRequest(`/tournaments/${tournamentId}/participants/${participantId}.json`, 'PUT', {
      participant: participantData
    });
    return response.participant;
  }

  async removeParticipant(tournamentId: string, participantId: string): Promise<void> {
    await this.makeRequest(`/tournaments/${tournamentId}/participants/${participantId}.json`, 'DELETE');
  }

  async getMatches(tournamentId: string): Promise<Match[]> {
    const response = await this.makeRequest(`/tournaments/${tournamentId}/matches.json`);
    return response.map((item: any) => item.match);
  }

  async getMatch(tournamentId: string, matchId: string): Promise<Match> {
    const response = await this.makeRequest(`/tournaments/${tournamentId}/matches/${matchId}.json`);
    return response.match;
  }

  async updateMatch(tournamentId: string, matchId: string, matchData: {
    winner_id?: string;
    scores_csv?: string;
    player1_votes?: number;
    player2_votes?: number;
  }): Promise<Match> {
    const response = await this.makeRequest(`/tournaments/${tournamentId}/matches/${matchId}.json`, 'PUT', {
      match: matchData
    });
    return response.match;
  }
}

export const challongeAPI = new ChallongeAPI();
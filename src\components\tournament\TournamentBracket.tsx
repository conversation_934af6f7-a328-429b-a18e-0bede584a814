'use client';

import { useEffect, useState } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Tournament, Participant, Match } from '@/lib/challonge';

interface TournamentBracketProps {
  tournamentId: string;
}

export default function TournamentBracket({ tournamentId }: TournamentBracketProps) {
  const [tournament, setTournament] = useState<Tournament | null>(null);
  const [participants, setParticipants] = useState<Participant[]>([]);
  const [matches, setMatches] = useState<Match[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    fetchTournamentData();
  }, [tournamentId]);

  const fetchTournamentData = async () => {
    try {
      setLoading(true);
      setError(null);

      const [tournamentRes, participantsRes, matchesRes] = await Promise.all([
        fetch(`/api/challonge/tournaments/${tournamentId}`),
        fetch(`/api/challonge/participants?tournamentId=${tournamentId}`),
        fetch(`/api/challonge/matches?tournamentId=${tournamentId}`)
      ]);

      if (!tournamentRes.ok || !participantsRes.ok || !matchesRes.ok) {
        throw new Error('Failed to fetch tournament data');
      }

      const tournamentData = await tournamentRes.json();
      const participantsData = await participantsRes.json();
      const matchesData = await matchesRes.json();

      setTournament(tournamentData.tournament);
      setParticipants(participantsData.participants);
      setMatches(matchesData.matches);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred');
    } finally {
      setLoading(false);
    }
  };

  const handleTournamentAction = async (action: string) => {
    try {
      const response = await fetch(`/api/challonge/tournaments/${tournamentId}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ action })
      });

      if (!response.ok) {
        throw new Error(`Failed to ${action} tournament`);
      }

      fetchTournamentData();
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred');
    }
  };

  const handleMatchUpdate = async (matchId: string, winnerId: string, scores: string) => {
    try {
      const response = await fetch('/api/challonge/matches', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          tournamentId,
          matchId,
          winner_id: winnerId,
          scores_csv: scores
        })
      });

      if (!response.ok) {
        throw new Error('Failed to update match');
      }

      fetchTournamentData();
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred');
    }
  };

  const getParticipantName = (participantId: string | null): string => {
    if (!participantId) return 'TBD';
    const participant = participants.find(p => p.id === participantId);
    return participant?.name || 'Unknown';
  };

  const organizeMatchesByRound = (matches: Match[]) => {
    const rounds: { [key: number]: Match[] } = {};
    
    matches.forEach(match => {
      if (!rounds[match.round]) {
        rounds[match.round] = [];
      }
      rounds[match.round].push(match);
    });

    return rounds;
  };

  if (loading) {
    return (
      <Card className="w-full max-w-4xl mx-auto bg-slate-800 border-slate-700">
        <CardContent className="p-6">
          <div className="text-center text-white">Loading tournament...</div>
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card className="w-full max-w-4xl mx-auto bg-slate-800 border-slate-700">
        <CardContent className="p-6">
          <div className="text-center text-red-400">Error: {error}</div>
          <Button onClick={fetchTournamentData} className="mt-4 bg-blue-600 hover:bg-blue-700">
            Retry
          </Button>
        </CardContent>
      </Card>
    );
  }

  if (!tournament) {
    return (
      <Card className="w-full max-w-4xl mx-auto bg-slate-800 border-slate-700">
        <CardContent className="p-6">
          <div className="text-center text-white">Tournament not found</div>
        </CardContent>
      </Card>
    );
  }

  const rounds = organizeMatchesByRound(matches);
  const roundNumbers = Object.keys(rounds).map(Number).sort((a, b) => a - b);

  return (
    <div className="w-full max-w-7xl mx-auto space-y-6">
      <Card className="bg-slate-800 border-slate-700">
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="text-2xl font-bold text-white">{tournament.name}</CardTitle>
            <div className={`px-3 py-1 rounded-full text-sm font-medium ${
              tournament.state === 'pending' ? 'bg-yellow-900/50 text-yellow-400 border border-yellow-600/50' :
              tournament.state === 'underway' ? 'bg-blue-900/50 text-blue-400 border border-blue-600/50' :
              tournament.state === 'complete' ? 'bg-green-900/50 text-green-400 border border-green-600/50' :
              'bg-gray-900/50 text-gray-400 border border-gray-600/50'
            }`}>
              {tournament.state.charAt(0).toUpperCase() + tournament.state.slice(1)}
            </div>
          </div>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm text-gray-300 mt-4">
            <div>
              <span className="text-gray-400">Type:</span> {tournament.tournament_type}
            </div>
            <div>
              <span className="text-gray-400">Participants:</span> {tournament.participants_count}
            </div>
            {tournament.game_name && (
              <div>
                <span className="text-gray-400">Game:</span> {tournament.game_name}
              </div>
            )}
            {tournament.description && (
              <div className="col-span-full">
                <span className="text-gray-400">Description:</span> {tournament.description}
              </div>
            )}
          </div>
        </CardHeader>
        <CardContent>
          <div className="flex gap-2 mb-4">
            {tournament.state === 'pending' && (
              <Button onClick={() => handleTournamentAction('start')} className="bg-green-600 hover:bg-green-700">
                Start Tournament
              </Button>
            )}
            {tournament.state === 'underway' && (
              <Button onClick={() => handleTournamentAction('finalize')} className="bg-blue-600 hover:bg-blue-700">
                Finalize Tournament
              </Button>
            )}
            {tournament.state !== 'pending' && (
              <Button 
                onClick={() => handleTournamentAction('reset')}
                variant="outline"
                className="border-slate-500 text-white hover:bg-slate-700"
              >
                Reset Tournament
              </Button>
            )}
          </div>
        </CardContent>
      </Card>

      {participants.length > 0 && (
        <Card className="bg-slate-800 border-slate-700">
          <CardHeader>
            <CardTitle className="text-white">Participants ({participants.length})</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-6 gap-3">
              {participants
                .sort((a, b) => a.seed - b.seed)
                .map(participant => (
                <div key={participant.id} className="p-2 border border-slate-600 rounded-lg bg-slate-700 hover:bg-slate-650 transition-colors">
                  <div className="flex items-center gap-2">
                    <div className="w-6 h-6 bg-blue-600 rounded-full flex items-center justify-center text-xs font-bold text-white">
                      {participant.seed}
                    </div>
                    <div className="flex-1 min-w-0">
                      <div className="font-medium text-white text-sm truncate">{participant.name}</div>
                      {participant.final_rank && (
                        <div className="text-xs text-green-400">Rank: {participant.final_rank}</div>
                      )}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {matches.length > 0 && (
        <Card className="bg-slate-800 border-slate-700">
          <CardHeader>
            <CardTitle className="text-white">Bracket</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="overflow-x-auto">
              <div className="flex gap-8 min-w-max pb-4">
                {roundNumbers.map((roundNumber, roundIndex) => (
                  <div key={roundNumber} className="flex flex-col space-y-3 min-w-[280px] relative">
                    <h3 className="font-semibold text-lg text-white text-center sticky top-0 bg-slate-800 py-2 border-b border-slate-600">
                      Round {roundNumber}
                      {roundNumber === Math.max(...roundNumbers) && rounds[roundNumber]?.length === 1 && ' (Final)'}
                    </h3>

                    {/* Connecting line to next round */}
                    {roundIndex < roundNumbers.length - 1 && (
                      <div className="absolute top-16 -right-4 w-8 h-full pointer-events-none">
                        <div className="w-full h-full flex items-center">
                          <div className="w-full h-0.5 bg-slate-600"></div>
                          <div className="absolute right-0 w-2 h-2 bg-slate-600 rotate-45 transform translate-x-1"></div>
                        </div>
                      </div>
                    )}
                    <div className="space-y-3">
                      {(rounds[roundNumber] || []).map(match => (
                        <div key={match.id} className="border border-slate-600 rounded-lg p-3 bg-slate-700 hover:bg-slate-650 transition-colors">
                          <div className="space-y-2">
                            <div className="space-y-1">
                              <div className={`flex items-center justify-between p-2 rounded ${match.winner_id === match.player1_id ? 'bg-green-900/30 border border-green-600/50' : 'bg-slate-600/50'}`}>
                                <span className={`font-medium text-sm ${match.winner_id === match.player1_id ? 'text-green-400' : 'text-white'}`}>
                                  {getParticipantName(match.player1_id)}
                                </span>
                                {match.winner_id === match.player1_id && (
                                  <span className="text-green-400 text-xs">✓</span>
                                )}
                              </div>
                              <div className="text-center text-xs text-gray-400 py-1">vs</div>
                              <div className={`flex items-center justify-between p-2 rounded ${match.winner_id === match.player2_id ? 'bg-green-900/30 border border-green-600/50' : 'bg-slate-600/50'}`}>
                                <span className={`font-medium text-sm ${match.winner_id === match.player2_id ? 'text-green-400' : 'text-white'}`}>
                                  {getParticipantName(match.player2_id)}
                                </span>
                                {match.winner_id === match.player2_id && (
                                  <span className="text-green-400 text-xs">✓</span>
                                )}
                              </div>
                            </div>

                            {match.scores_csv && (
                              <div className="text-xs text-gray-300 text-center bg-slate-600/30 rounded px-2 py-1">
                                {match.scores_csv}
                              </div>
                            )}

                            <div className="text-xs text-gray-400 text-center">
                              {match.state === 'complete' ? 'Complete' : match.state === 'open' ? 'In Progress' : 'Pending'}
                            </div>

                            {match.state === 'open' && match.player1_id && match.player2_id && (
                              <div className="space-y-2 pt-2 border-t border-slate-600">
                                <div className="flex flex-col gap-1">
                                  <Button
                                    size="sm"
                                    onClick={() => handleMatchUpdate(match.id, match.player1_id!, '2-0')}
                                    className="bg-green-600 hover:bg-green-700 text-xs py-1 h-auto"
                                  >
                                    {getParticipantName(match.player1_id)} Wins
                                  </Button>
                                  <Button
                                    size="sm"
                                    onClick={() => handleMatchUpdate(match.id, match.player2_id!, '0-2')}
                                    className="bg-green-600 hover:bg-green-700 text-xs py-1 h-auto"
                                  >
                                    {getParticipantName(match.player2_id)} Wins
                                  </Button>
                                </div>
                              </div>
                            )}
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}